"use client"

import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEffect, useState, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useAuthStore } from '@/lib/stores/authStore';
import { api } from '@/lib/services/api';

export interface TicketNotification {
  id: string;
  title: string;
  status: 'resolved' | 'waiting_on_customer';
  updated_at: string;
  creator?: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{
    target_type: string;
    target_id: number;
  }>;
}

export interface NotificationData {
  notifications: TicketNotification[];
  unreadCount: number;
  lastSeenAt: string;
}

export function useNotifications() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const supabase = createClient();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Fetch notifications
  const {
    data: notificationData,
    isLoading,
    error,
    refetch
  } = useQuery<NotificationData>({
    queryKey: ['notifications'],
    queryFn: () => api.getNotifications(50),
    enabled: !!user,
    refetchInterval: 30000, // Refetch every 30 seconds as fallback
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Mark notifications as seen mutation
  const markAsSeenMutation = useMutation({
    mutationFn: api.markNotificationsAsSeen,
    onSuccess: (data) => {
      // Update the cache with new lastSeenAt
      queryClient.setQueryData(['notifications'], (oldData: NotificationData | undefined) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          unreadCount: 0,
          lastSeenAt: data.lastSeenAt
        };
      });
    },
    onError: (error) => {
      console.error('Failed to mark notifications as seen:', error);
    }
  });

  // Realtime subscription for ticket updates
  useEffect(() => {
    if (!user?.id) {
      console.log('No user ID available, skipping realtime subscription');
      return;
    }

    console.log('Setting up notifications realtime subscription for user:', user.id);

    const channel = supabase
      .channel(`notifications-${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tickets',
          filter: `creator_id=eq.${user.id}`
        },
        (payload) => {
          console.log('Ticket update received:', payload);
          
          const oldRecord = payload.old as any;
          const newRecord = payload.new as any;
          
          // Only trigger notification for status changes to resolved or waiting_on_customer
          if (
            (newRecord.status === 'resolved' || newRecord.status === 'waiting_on_customer') &&
            oldRecord.status !== newRecord.status
          ) {
            console.log('Relevant status change detected, refetching notifications');
            // Refetch notifications to get the latest data
            refetch();
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up notifications realtime subscription');
      supabase.removeChannel(channel);
    };
  }, [user?.id, refetch, supabase]);

  // Handle dropdown open/close
  const handleDropdownToggle = useCallback(() => {
    const newIsOpen = !isDropdownOpen;
    setIsDropdownOpen(newIsOpen);
    
    // Mark as seen when opening dropdown
    if (newIsOpen && notificationData?.unreadCount && notificationData.unreadCount > 0) {
      markAsSeenMutation.mutate();
    }
  }, [isDropdownOpen, notificationData?.unreadCount, markAsSeenMutation]);

  const handleDropdownClose = useCallback(() => {
    setIsDropdownOpen(false);
  }, []);

  return {
    notifications: notificationData?.notifications || [],
    unreadCount: notificationData?.unreadCount || 0,
    lastSeenAt: notificationData?.lastSeenAt,
    isLoading,
    error,
    isDropdownOpen,
    handleDropdownToggle,
    handleDropdownClose,
    refetch
  };
}
